# Lacan Structure Classifier Results

## Overview
This classifier was trained to distinguish between 4 Lacanian psychological structures using text quotes from various individuals:
- **Detached** (17 samples)
- **Psychotic** (23 samples) 
- **Neurotic** (10 samples)
- **Pervert** (5 samples)

## Data Filtering Applied
- Removed "Seminal" structure entries (insufficient data)
- Removed <PERSON> (changed types over time)
- Removed entries without proper quotes (placeholder text)
- Final dataset: **55 samples** from original 64

## Model Configuration
- **Embeddings**: all-MiniLM-L6-v2 (384-dimensional)
- **Classifier**: Support Vector Classifier (SVC)
- **Cross-validation**: 5-fold stratified
- **Class balancing**: Applied to handle imbalanced dataset
- **Feature scaling**: StandardScaler applied
- **Hyperparameter tuning**: Grid search performed

## Best Parameters Found
- **C**: 0.1
- **Kernel**: linear
- **Gamma**: scale

## Performance Results

### Cross-Validation Scores
- Fold 1: 45.45%
- Fold 2: 45.45%
- Fold 3: 54.55%
- Fold 4: 63.64%
- Fold 5: 45.45%

**Mean CV Accuracy: 50.91% (±14.55%)**

### Detailed Classification Report
```
              precision    recall  f1-score   support

    Detached       0.50      0.41      0.45        17
    Neurotic       0.71      0.50      0.59        10
     Pervert       0.00      0.00      0.00         5
   Psychotic       0.48      0.70      0.57        23

    accuracy                           0.51        55
   macro avg       0.42      0.40      0.40        55
weighted avg       0.49      0.51      0.49        55
```

## Key Observations

### Strengths
1. **Neurotic class** shows best precision (71%) but moderate recall (50%)
2. **Psychotic class** has highest recall (70%) indicating good detection
3. Overall accuracy of ~51% is above random chance (25% for 4 classes)

### Challenges
1. **Pervert class** completely failed (0% precision/recall) - likely due to very small sample size (5 samples)
2. **Class imbalance** remains an issue despite balancing techniques
3. **High variance** in CV scores (±14.55%) suggests model instability
4. Many samples predicted as **Psychotic** (possible model bias toward largest class)

### Sample Predictions Analysis
- Model tends to over-predict "Psychotic" structure
- Some clear misclassifications (e.g., Temple Grandin: Detached → Psychotic)
- Successful predictions include Donald Trump and Jordan Peterson (both Psychotic)

## Recommendations for Improvement

1. **Collect more data**, especially for underrepresented classes (Pervert, Neurotic)
2. **Feature engineering**: Try different embedding models or combine multiple embeddings
3. **Ensemble methods**: Combine multiple classifiers
4. **Text preprocessing**: More sophisticated cleaning and normalization
5. **Domain-specific embeddings**: Train embeddings on psychology/personality texts
6. **Manual feature extraction**: Add linguistic features (sentence length, complexity, etc.)

## Files Generated
- `lacan_classifier.py` - Main classifier script
- `lacan_classifier_results.png` - Confusion matrix and CV scores visualization
- `classifier_summary.md` - This summary report

## Usage
```bash
python lacan_classifier.py
```

The script will automatically:
1. Load and filter the CSV data
2. Create embeddings using MiniLM
3. Train SVC with 5-fold cross-validation
4. Generate performance metrics and visualizations
5. Save results plot as PNG file
