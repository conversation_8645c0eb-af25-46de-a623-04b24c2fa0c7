#!/usr/bin/env python3
"""
Simple runner for Lacan Classifier Suite
Easy interface to test different approaches
"""

from lacan_classifier_suite import LacanClassifierSuite

def main():
    print("🧠 Lacan Structure Classifier Suite")
    print("="*50)
    
    suite = LacanClassifierSuite()
    
    print("\nAvailable approaches:")
    print("1. original_4class - Original 4-class (imbalanced)")
    print("2. 3class_balanced_min - 3-class balanced to minimum")
    print("3. 3class_balanced_median - 3-class balanced to median")
    print("4. lumped_2class - Lumped categories (IMBALANCED)")
    print("5. lumped_2class_balanced - Lumped categories (BALANCED)")
    print("6. svr_original - SVR with original ordinal encoding")
    print("7. svr_severity - SVR with severity-based encoding")
    print("8. svr_social - SVR with social-functioning encoding")
    print("9. all - Run all approaches and compare")
    
    choice = input("\nEnter choice (1-9 or approach name): ").strip()

    # Map numbers to approach names
    approach_map = {
        '1': 'original_4class',
        '2': '3class_balanced_min',
        '3': '3class_balanced_median',
        '4': 'lumped_2class',
        '5': 'lumped_2class_balanced',
        '6': 'svr_original',
        '7': 'svr_severity',
        '8': 'svr_social',
        '9': 'all'
    }
    
    approach = approach_map.get(choice, choice)
    
    if approach == 'all':
        print("\n🚀 Running comprehensive comparison...")
        suite.run_comparison()
    else:
        print(f"\n🚀 Running {approach}...")
        try:
            suite.evaluate_approach(approach)
        except ValueError as e:
            print(f"❌ Error: {e}")
            print("Valid approaches:", list(approach_map.values())[:-1])

if __name__ == "__main__":
    main()
