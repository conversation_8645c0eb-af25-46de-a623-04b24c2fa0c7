#!/usr/bin/env python3
"""
Lacan Structure Classifier using SVR with alternative ordinal encoding
Tries different ordinal mappings that might be more theoretically meaningful
"""

import pandas as pd
import numpy as np
from sklearn.svm import SVR
from sklearn.model_selection import StratifiedKFold, cross_val_score, GridSearchCV
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import classification_report, confusion_matrix, mean_squared_error
from sentence_transformers import SentenceTransformer
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

def load_and_filter_data(csv_path):
    """Load CSV and apply all filtering criteria"""
    print("Loading data from CSV...")
    df = pd.read_csv(csv_path)
    
    print(f"Original dataset size: {len(df)} entries")
    
    # Filter out Seminal structure (not enough data)
    df = df[df['Structure'] != 'Seminal']
    print(f"After removing Seminal structure: {len(df)} entries")
    
    # Filter out Taylor Swift (changed types)
    df = df[df['Name/Identifier'] != 'Taylor Swift']
    print(f"After removing Taylor Swift: {len(df)} entries")
    
    # Filter out entries without proper quotes
    quote_filters = [
        r'\[Speaking example not available',
        r'\[Source link needed\]',
        r'\[Source name needed\]',
        'While specific extensive quotes',
        'Based on the available information'
    ]
    
    for filter_text in quote_filters:
        df = df[~df['Quote'].str.contains(filter_text, na=False, regex=True)]
    
    # Also filter out any entries with NaN quotes
    df = df.dropna(subset=['Quote'])
    
    print(f"After removing entries without proper quotes: {len(df)} entries")
    
    # Keep only the 4 main structures
    valid_structures = ['Detached', 'Psychotic', 'Neurotic', 'Pervert']
    df = df[df['Structure'].isin(valid_structures)]
    print(f"After keeping only main 4 structures: {len(df)} entries")
    
    # Show class distribution
    print("\nClass distribution:")
    print(df['Structure'].value_counts())
    
    return df

def preprocess_text(text):
    """Basic text preprocessing"""
    import re
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text)
    # Remove special characters but keep punctuation that might be meaningful
    text = re.sub(r'[^\w\s\.\!\?\,\;\:\-\'\"]', ' ', text)
    return text.strip()

def create_embeddings(texts, model_name='all-MiniLM-L6-v2'):
    """Create embeddings using MiniLM model"""
    print(f"Creating embeddings using {model_name}...")
    
    # Preprocess texts
    print("Preprocessing texts...")
    processed_texts = [preprocess_text(text) for text in texts]
    
    model = SentenceTransformer(model_name)
    embeddings = model.encode(processed_texts, show_progress_bar=True)
    print(f"Created embeddings with shape: {embeddings.shape}")
    return embeddings

def test_multiple_encodings(structures):
    """Test different ordinal encodings"""
    
    # Encoding 1: Original (Detached=0, Psychotic=1, Neurotic=2, Pervert=3)
    encoding1 = {
        'Detached': 0,
        'Psychotic': 1, 
        'Neurotic': 2,
        'Pervert': 3
    }
    
    # Encoding 2: Based on severity/intensity (Detached=0, Neurotic=1, Pervert=2, Psychotic=3)
    encoding2 = {
        'Detached': 0,
        'Neurotic': 1,
        'Pervert': 2,
        'Psychotic': 3
    }
    
    # Encoding 3: Based on social functioning (Psychotic=0, Detached=1, Pervert=2, Neurotic=3)
    encoding3 = {
        'Psychotic': 0,
        'Detached': 1,
        'Pervert': 2,
        'Neurotic': 3
    }
    
    encodings = {
        'Original': encoding1,
        'Severity-based': encoding2,
        'Social-functioning': encoding3
    }
    
    results = {}
    
    for name, encoding in encodings.items():
        encoded = [encoding[s] for s in structures]
        reverse_encoding = {v: k for k, v in encoding.items()}
        results[name] = (np.array(encoded), encoding, reverse_encoding)
        
        print(f"\n{name} encoding mapping:")
        for struct, num in encoding.items():
            count = structures.count(struct)
            print(f"{num}: {struct} ({count} samples)")
    
    return results

def train_and_evaluate_svr(X, y, encoding_name, cv_folds=5):
    """Train SVR with cross-validation"""
    print(f"\nTraining SVR with {encoding_name} encoding...")
    
    # Scale features
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # Initialize SVR with simpler parameter grid for faster execution
    svr = SVR()
    
    # Simplified grid search
    param_grid = {
        'C': [0.1, 1.0, 10.0],
        'kernel': ['rbf', 'linear'],
        'gamma': ['scale', 'auto'],
        'epsilon': [0.01, 0.1]
    }
    
    # For regression, we need to use StratifiedKFold based on rounded y values
    y_discrete = np.round(y).astype(int)
    skf = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42)
    
    grid_search = GridSearchCV(svr, param_grid, cv=skf, scoring='neg_mean_squared_error', n_jobs=-1)
    grid_search.fit(X_scaled, y)
    
    print(f"Best parameters: {grid_search.best_params_}")
    print(f"Best CV MSE: {-grid_search.best_score_:.4f}")
    
    # Get the best model
    best_svr = grid_search.best_estimator_
    
    # Perform cross-validation with best model
    from sklearn.model_selection import cross_val_predict
    y_pred_continuous = cross_val_predict(best_svr, X_scaled, y, cv=skf)
    
    # Round predictions to nearest integer for classification
    y_pred_discrete = np.round(y_pred_continuous).astype(int)
    # Clip to valid range
    max_val = int(np.max(y))
    y_pred_discrete = np.clip(y_pred_discrete, 0, max_val)
    
    # Calculate MSE for regression performance
    mse = mean_squared_error(y, y_pred_continuous)
    
    # Calculate classification accuracy after rounding
    y_discrete = np.round(y).astype(int)
    accuracy = np.mean(y_discrete == y_pred_discrete)
    
    print(f"MSE: {mse:.4f}, RMSE: {np.sqrt(mse):.4f}, Accuracy: {accuracy:.4f}")
    
    return best_svr, y_pred_continuous, y_pred_discrete, mse, accuracy

def compare_encodings(df, X):
    """Compare different ordinal encodings"""
    structures = df['Structure'].tolist()
    encodings_results = test_multiple_encodings(structures)
    
    comparison_results = {}
    
    for encoding_name, (y, structure_to_num, num_to_structure) in encodings_results.items():
        model, y_pred_cont, y_pred_disc, mse, accuracy = train_and_evaluate_svr(X, y, encoding_name)
        
        # Get classification report
        y_discrete = np.round(y).astype(int)
        class_names = [num_to_structure[i] for i in sorted(num_to_structure.keys())]
        
        comparison_results[encoding_name] = {
            'accuracy': accuracy,
            'mse': mse,
            'rmse': np.sqrt(mse),
            'y_pred_continuous': y_pred_cont,
            'y_pred_discrete': y_pred_disc,
            'y_true': y_discrete,
            'class_names': class_names,
            'num_to_structure': num_to_structure
        }
        
        print(f"\n{encoding_name} Classification Report:")
        print(classification_report(y_discrete, y_pred_disc, target_names=class_names, zero_division=0))
    
    return comparison_results

def plot_comparison(comparison_results):
    """Plot comparison of different encodings"""
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    encoding_names = list(comparison_results.keys())
    
    for i, encoding_name in enumerate(encoding_names):
        results = comparison_results[encoding_name]
        
        # Confusion matrix
        cm = confusion_matrix(results['y_true'], results['y_pred_discrete'])
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                    xticklabels=results['class_names'], 
                    yticklabels=results['class_names'], 
                    ax=axes[0, i])
        axes[0, i].set_title(f'{encoding_name}\nAccuracy: {results["accuracy"]:.3f}')
        axes[0, i].set_xlabel('Predicted')
        axes[0, i].set_ylabel('Actual')
        
        # Scatter plot
        axes[1, i].scatter(results['y_true'], results['y_pred_continuous'], alpha=0.6)
        max_val = max(np.max(results['y_true']), np.max(results['y_pred_continuous']))
        axes[1, i].plot([0, max_val], [0, max_val], 'r--', label='Perfect prediction')
        axes[1, i].set_xlabel('True Values')
        axes[1, i].set_ylabel('Predicted Values')
        axes[1, i].set_title(f'RMSE: {results["rmse"]:.3f}')
        axes[1, i].legend()
        axes[1, i].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('lacan_classifier_svr_comparison.png', dpi=300, bbox_inches='tight')
    print("Comparison plot saved as 'lacan_classifier_svr_comparison.png'")
    plt.close()

def main():
    """Main execution function"""
    # Load and filter data
    df = load_and_filter_data('enhanced_classes.csv')
    
    if len(df) == 0:
        print("No data remaining after filtering!")
        return
    
    # Prepare data
    texts = df['Quote'].tolist()
    
    # Create embeddings
    X = create_embeddings(texts)
    
    # Compare different encodings
    print("\n" + "="*60)
    print("COMPARING DIFFERENT ORDINAL ENCODINGS")
    print("="*60)
    
    comparison_results = compare_encodings(df, X)
    
    # Plot comparison
    plot_comparison(comparison_results)
    
    # Summary
    print("\n" + "="*60)
    print("SUMMARY OF RESULTS")
    print("="*60)
    
    for encoding_name, results in comparison_results.items():
        print(f"{encoding_name:20} | Accuracy: {results['accuracy']:.4f} | RMSE: {results['rmse']:.4f}")
    
    # Find best encoding
    best_encoding = max(comparison_results.keys(), key=lambda x: comparison_results[x]['accuracy'])
    print(f"\nBest encoding by accuracy: {best_encoding} ({comparison_results[best_encoding]['accuracy']:.4f})")

if __name__ == "__main__":
    main()
