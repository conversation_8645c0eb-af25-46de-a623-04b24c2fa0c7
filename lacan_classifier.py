#!/usr/bin/env python3
"""
Lacan Structure Classifier using SVC with 5-fold cross-validation
Classifies text into 4 Lacanian structures: Detached, Psychotic, Neurotic, Pervert
"""

import pandas as pd
import numpy as np
from sklearn.svm import SVC
from sklearn.model_selection import StratifiedKFold, cross_val_score
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import classification_report, confusion_matrix
from sentence_transformers import SentenceTransformer
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

def load_and_filter_data(csv_path):
    """Load CSV and apply all filtering criteria"""
    print("Loading data from CSV...")
    df = pd.read_csv(csv_path)
    
    print(f"Original dataset size: {len(df)} entries")
    
    # Filter out Seminal structure (not enough data)
    df = df[df['Structure'] != 'Seminal']
    print(f"After removing Seminal structure: {len(df)} entries")
    
    # Filter out Taylor Swift (changed types)
    df = df[df['Name/Identifier'] != 'Taylor Swift']
    print(f"After removing Taylor Swift: {len(df)} entries")
    
    # Filter out entries without proper quotes
    # Look for entries with placeholder text or missing quotes
    quote_filters = [
        r'\[Speaking example not available',
        r'\[Source link needed\]',
        r'\[Source name needed\]',
        'While specific extensive quotes',
        'Based on the available information'
    ]

    for filter_text in quote_filters:
        df = df[~df['Quote'].str.contains(filter_text, na=False, regex=True)]
    
    # Also filter out any entries with NaN quotes
    df = df.dropna(subset=['Quote'])
    
    print(f"After removing entries without proper quotes: {len(df)} entries")
    
    # Keep only the 4 main structures
    valid_structures = ['Detached', 'Psychotic', 'Neurotic', 'Pervert']
    df = df[df['Structure'].isin(valid_structures)]
    print(f"After keeping only main 4 structures: {len(df)} entries")
    
    # Show class distribution
    print("\nClass distribution:")
    print(df['Structure'].value_counts())
    
    return df

def preprocess_text(text):
    """Basic text preprocessing"""
    import re
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text)
    # Remove special characters but keep punctuation that might be meaningful
    text = re.sub(r'[^\w\s\.\!\?\,\;\:\-\'\"]', ' ', text)
    return text.strip()

def create_embeddings(texts, model_name='all-MiniLM-L6-v2'):
    """Create embeddings using MiniLM model"""
    print(f"Creating embeddings using {model_name}...")

    # Preprocess texts
    print("Preprocessing texts...")
    processed_texts = [preprocess_text(text) for text in texts]

    model = SentenceTransformer(model_name)
    embeddings = model.encode(processed_texts, show_progress_bar=True)
    print(f"Created embeddings with shape: {embeddings.shape}")
    return embeddings

def train_and_evaluate_classifier(X, y, cv_folds=5):
    """Train SVC classifier with stratified k-fold cross-validation"""
    print(f"\nTraining SVC classifier with {cv_folds}-fold cross-validation...")

    # Try different SVC parameters for better performance
    from sklearn.preprocessing import StandardScaler
    from sklearn.pipeline import Pipeline
    from sklearn.model_selection import GridSearchCV

    # Scale features
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)

    # Initialize classifier with different parameters to try
    svc = SVC(random_state=42, class_weight='balanced')  # balanced to handle class imbalance

    # Initialize stratified k-fold
    skf = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42)

    # Try a simple grid search for better parameters
    param_grid = {
        'C': [0.1, 1.0, 10.0],
        'kernel': ['rbf', 'linear'],
        'gamma': ['scale', 'auto']
    }

    print("Performing grid search for best parameters...")
    grid_search = GridSearchCV(svc, param_grid, cv=skf, scoring='accuracy', n_jobs=-1)
    grid_search.fit(X_scaled, y)

    print(f"Best parameters: {grid_search.best_params_}")
    print(f"Best CV score: {grid_search.best_score_:.4f}")

    # Get the best model
    best_svc = grid_search.best_estimator_

    # Perform cross-validation with best model
    cv_scores = cross_val_score(best_svc, X_scaled, y, cv=skf, scoring='accuracy')

    print(f"Cross-validation scores with best model: {cv_scores}")
    print(f"Mean CV accuracy: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")

    # Get predictions for confusion matrix (using cross-validation predictions)
    from sklearn.model_selection import cross_val_predict
    y_pred = cross_val_predict(best_svc, X_scaled, y, cv=skf)

    return best_svc, cv_scores, y_pred

def plot_results(y_true, y_pred, class_names, cv_scores):
    """Plot confusion matrix and CV scores"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Confusion Matrix
    cm = confusion_matrix(y_true, y_pred)
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=class_names, yticklabels=class_names, ax=ax1)
    ax1.set_title('Confusion Matrix')
    ax1.set_xlabel('Predicted')
    ax1.set_ylabel('Actual')
    
    # CV Scores
    ax2.bar(range(len(cv_scores)), cv_scores, alpha=0.7)
    ax2.axhline(y=cv_scores.mean(), color='red', linestyle='--', 
                label=f'Mean: {cv_scores.mean():.4f}')
    ax2.set_title('Cross-Validation Scores')
    ax2.set_xlabel('Fold')
    ax2.set_ylabel('Accuracy')
    ax2.legend()
    ax2.set_ylim(0, 1)
    
    plt.tight_layout()
    plt.savefig('lacan_classifier_results.png', dpi=300, bbox_inches='tight')
    print("Results plot saved as 'lacan_classifier_results.png'")
    plt.close()

def main():
    """Main execution function"""
    # Load and filter data
    df = load_and_filter_data('enhanced_classes.csv')
    
    if len(df) == 0:
        print("No data remaining after filtering!")
        return
    
    # Prepare data
    texts = df['Quote'].tolist()
    labels = df['Structure'].tolist()
    
    # Create embeddings
    X = create_embeddings(texts)
    
    # Encode labels
    le = LabelEncoder()
    y = le.fit_transform(labels)
    class_names = le.classes_
    
    print(f"\nClass mapping:")
    for i, class_name in enumerate(class_names):
        print(f"{i}: {class_name}")
    
    # Check class balance for stratification
    print(f"\nClass distribution for stratification:")
    class_counts = Counter(y)
    for class_idx, count in class_counts.items():
        print(f"{class_names[class_idx]}: {count} samples")
    
    # Train and evaluate
    model, cv_scores, y_pred = train_and_evaluate_classifier(X, y)
    
    # Print detailed classification report
    print("\nDetailed Classification Report:")
    print(classification_report(y, y_pred, target_names=class_names))
    
    # Plot results
    plot_results(y, y_pred, class_names, cv_scores)
    
    # Save some example predictions
    print("\nSample predictions:")
    for i in range(min(10, len(df))):
        actual = class_names[y[i]]
        predicted = class_names[y_pred[i]]
        name = df.iloc[i]['Name/Identifier']
        quote_preview = texts[i][:100] + "..." if len(texts[i]) > 100 else texts[i]
        print(f"\nName: {name}")
        print(f"Quote: {quote_preview}")
        print(f"Actual: {actual}, Predicted: {predicted}")
        print("-" * 50)

if __name__ == "__main__":
    main()
