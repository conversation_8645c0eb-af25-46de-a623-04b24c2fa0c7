#!/usr/bin/env python3
"""
Lacan Structure Classifier with balanced classes
- Filters out Pervert class (too few samples)
- Downsamples Psychotic class to balance with others
- Uses only 3 classes: Detached, Psychotic, Neurotic
"""

import pandas as pd
import numpy as np
from sklearn.svm import SVC
from sklearn.model_selection import StratifiedKFold, cross_val_score, GridSearchCV
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import classification_report, confusion_matrix
from sentence_transformers import SentenceTransformer
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

def load_and_filter_data(csv_path):
    """Load CSV and apply all filtering criteria"""
    print("Loading data from CSV...")
    df = pd.read_csv(csv_path)
    
    print(f"Original dataset size: {len(df)} entries")
    
    # Filter out Seminal structure (not enough data)
    df = df[df['Structure'] != 'Seminal']
    print(f"After removing Seminal structure: {len(df)} entries")
    
    # Filter out Taylor Swift (changed types)
    df = df[df['Name/Identifier'] != 'Taylor Swift']
    print(f"After removing Taylor Swift: {len(df)} entries")
    
    # Filter out entries without proper quotes
    quote_filters = [
        r'\[Speaking example not available',
        r'\[Source link needed\]',
        r'\[Source name needed\]',
        'While specific extensive quotes',
        'Based on the available information'
    ]
    
    for filter_text in quote_filters:
        df = df[~df['Quote'].str.contains(filter_text, na=False, regex=True)]
    
    # Also filter out any entries with NaN quotes
    df = df.dropna(subset=['Quote'])
    
    print(f"After removing entries without proper quotes: {len(df)} entries")
    
    # Keep only 3 main structures (remove Pervert due to insufficient data)
    valid_structures = ['Detached', 'Psychotic', 'Neurotic']
    df = df[df['Structure'].isin(valid_structures)]
    print(f"After keeping only 3 main structures (removed Pervert): {len(df)} entries")
    
    # Show class distribution before balancing
    print("\nClass distribution before balancing:")
    print(df['Structure'].value_counts())
    
    return df

def balance_classes(df, target_size=None, strategy='min'):
    """Balance classes by downsampling the majority class"""
    print("\nBalancing classes...")

    # Get class counts
    class_counts = df['Structure'].value_counts()
    print(f"Original counts: {dict(class_counts)}")

    # Determine target size based on strategy
    if target_size is None:
        if strategy == 'min':
            target_size = class_counts.min()
        elif strategy == 'median':
            target_size = int(class_counts.median())
        elif strategy == 'mean':
            target_size = int(class_counts.mean())
        else:
            target_size = class_counts.min()

    print(f"Target size per class ({strategy} strategy): {target_size}")

    # Sample each class to target size
    balanced_dfs = []
    for structure in class_counts.index:
        structure_df = df[df['Structure'] == structure]
        if len(structure_df) > target_size:
            # Downsample
            sampled_df = structure_df.sample(n=target_size, random_state=42)
            print(f"Downsampled {structure}: {len(structure_df)} → {len(sampled_df)}")
        else:
            # Keep all samples
            sampled_df = structure_df
            print(f"Kept all {structure}: {len(sampled_df)}")
        balanced_dfs.append(sampled_df)

    # Combine balanced datasets
    balanced_df = pd.concat(balanced_dfs, ignore_index=True)

    print(f"\nFinal balanced dataset size: {len(balanced_df)} entries")
    print("Final class distribution:")
    print(balanced_df['Structure'].value_counts())

    return balanced_df

def preprocess_text(text):
    """Basic text preprocessing"""
    import re
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text)
    # Remove special characters but keep punctuation that might be meaningful
    text = re.sub(r'[^\w\s\.\!\?\,\;\:\-\'\"]', ' ', text)
    return text.strip()

def create_embeddings(texts, model_name='all-MiniLM-L6-v2'):
    """Create embeddings using MiniLM model"""
    print(f"Creating embeddings using {model_name}...")
    
    # Preprocess texts
    print("Preprocessing texts...")
    processed_texts = [preprocess_text(text) for text in texts]
    
    model = SentenceTransformer(model_name)
    embeddings = model.encode(processed_texts, show_progress_bar=True)
    print(f"Created embeddings with shape: {embeddings.shape}")
    return embeddings

def train_and_evaluate_classifier(X, y, cv_folds=5):
    """Train SVC classifier with stratified k-fold cross-validation"""
    print(f"\nTraining SVC classifier with {cv_folds}-fold cross-validation...")
    
    # Scale features
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # Initialize classifier - try both balanced and unbalanced since we manually balanced
    svc = SVC(random_state=42)
    
    # Initialize stratified k-fold
    skf = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42)
    
    # Grid search parameters
    param_grid = {
        'C': [0.1, 1.0, 10.0],
        'kernel': ['rbf', 'linear'],
        'gamma': ['scale', 'auto'],
        'class_weight': [None, 'balanced']  # Try both since we manually balanced
    }
    
    print("Performing grid search for best parameters...")
    grid_search = GridSearchCV(svc, param_grid, cv=skf, scoring='accuracy', n_jobs=-1)
    grid_search.fit(X_scaled, y)
    
    print(f"Best parameters: {grid_search.best_params_}")
    print(f"Best CV score: {grid_search.best_score_:.4f}")
    
    # Get the best model
    best_svc = grid_search.best_estimator_
    
    # Perform cross-validation with best model
    cv_scores = cross_val_score(best_svc, X_scaled, y, cv=skf, scoring='accuracy')
    
    print(f"Cross-validation scores with best model: {cv_scores}")
    print(f"Mean CV accuracy: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")
    
    # Get predictions for confusion matrix (using cross-validation predictions)
    from sklearn.model_selection import cross_val_predict
    y_pred = cross_val_predict(best_svc, X_scaled, y, cv=skf)
    
    return best_svc, cv_scores, y_pred

def plot_results(y_true, y_pred, class_names, cv_scores):
    """Plot confusion matrix and CV scores"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Confusion Matrix
    cm = confusion_matrix(y_true, y_pred)
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=class_names, yticklabels=class_names, ax=ax1)
    ax1.set_title('Confusion Matrix (Balanced Dataset)')
    ax1.set_xlabel('Predicted')
    ax1.set_ylabel('Actual')
    
    # CV Scores
    ax2.bar(range(len(cv_scores)), cv_scores, alpha=0.7)
    ax2.axhline(y=cv_scores.mean(), color='red', linestyle='--', 
                label=f'Mean: {cv_scores.mean():.4f}')
    ax2.set_title('Cross-Validation Scores')
    ax2.set_xlabel('Fold')
    ax2.set_ylabel('Accuracy')
    ax2.legend()
    ax2.set_ylim(0, 1)
    
    plt.tight_layout()
    plt.savefig('lacan_classifier_balanced_results.png', dpi=300, bbox_inches='tight')
    print("Results plot saved as 'lacan_classifier_balanced_results.png'")
    plt.close()

def main():
    """Main execution function"""
    # Load and filter data
    df = load_and_filter_data('enhanced_classes.csv')
    
    if len(df) == 0:
        print("No data remaining after filtering!")
        return
    
    # Try different balancing strategies
    strategies = ['min', 'median']
    results = {}

    for strategy in strategies:
        print(f"\n{'='*60}")
        print(f"TRYING {strategy.upper()} BALANCING STRATEGY")
        print(f"{'='*60}")

        # Balance classes
        balanced_df = balance_classes(df, strategy=strategy)

        # Prepare data
        texts = balanced_df['Quote'].tolist()
        labels = balanced_df['Structure'].tolist()

        # Create embeddings
        X = create_embeddings(texts)

        # Encode labels
        le = LabelEncoder()
        y = le.fit_transform(labels)
        class_names = le.classes_

        print(f"\nClass mapping:")
        for i, class_name in enumerate(class_names):
            print(f"{i}: {class_name}")

        # Check final class balance
        print(f"\nFinal class distribution for training:")
        class_counts = Counter(y)
        for class_idx, count in class_counts.items():
            print(f"{class_names[class_idx]}: {count} samples")

        # Train and evaluate
        model, cv_scores, y_pred = train_and_evaluate_classifier(X, y)

        # Store results
        results[strategy] = {
            'accuracy': cv_scores.mean(),
            'std': cv_scores.std(),
            'cv_scores': cv_scores,
            'y_pred': y_pred,
            'y_true': y,
            'class_names': class_names,
            'dataset_size': len(balanced_df)
        }

        # Print detailed classification report
        print("\nDetailed Classification Report:")
        print(classification_report(y, y_pred, target_names=class_names))

        # Plot results for this strategy
        plot_results(y, y_pred, class_names, cv_scores)

        # Rename the plot file to include strategy
        import os
        old_name = 'lacan_classifier_balanced_results.png'
        new_name = f'lacan_classifier_balanced_{strategy}_results.png'
        if os.path.exists(old_name):
            os.rename(old_name, new_name)
            print(f"Results plot saved as '{new_name}'")

    # Compare strategies
    print(f"\n" + "="*60)
    print("COMPARISON OF BALANCING STRATEGIES")
    print("="*60)

    for strategy, result in results.items():
        print(f"{strategy.upper():10} | Accuracy: {result['accuracy']:.4f} (±{result['std']*2:.4f}) | Dataset size: {result['dataset_size']}")

    # Find best strategy
    best_strategy = max(results.keys(), key=lambda x: results[x]['accuracy'])
    best_accuracy = results[best_strategy]['accuracy']

    print(f"\nBest strategy: {best_strategy.upper()} with {best_accuracy:.4f} accuracy")

    # Compare with original results
    print(f"\n" + "="*60)
    print("COMPARISON WITH PREVIOUS RESULTS")
    print("="*60)
    print(f"Best balanced 3-class accuracy: {best_accuracy:.4f}")
    print(f"Previous 4-class accuracy: 50.91%")
    print(f"Previous 4-class had Pervert: 0% precision/recall (5 samples)")
    print(f"Balanced dataset removes class imbalance issues")

if __name__ == "__main__":
    main()
