#!/usr/bin/env python3
"""
Exa API Quote Scraper

This script uses the Exa API to find quotes and writing samples for individuals
listed in a CSV file. It searches for interviews, articles, tweets, and other
authentic speech/writing examples.

Usage:
    python exa_quotes_scraper.py

Requirements:
    - exa_py
    - pandas
    - python-dotenv
    - requests

Environment Variables:
    - EXA_API_KEY: Your Exa API key
"""

import os
import csv
import time
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
import pandas as pd
from exa_py import Exa
from dotenv import load_dotenv
import re

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('exa_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ExaQuoteScraper:
    def __init__(self, api_key: str):
        """Initialize the Exa Quote Scraper."""
        self.exa = Exa(api_key)
        self.rate_limit_delay = 1.0  # Delay between API calls in seconds
        
    def clean_name(self, name: str) -> str:
        """Clean the name by removing social media handles and extra info."""
        # Remove content in parentheses and brackets
        name = re.sub(r'\([^)]*\)', '', name)
        name = re.sub(r'\[[^\]]*\]', '', name)
        
        # Remove social media handles (@username)
        name = re.sub(r'@\w+', '', name)
        
        # Remove extra descriptive text after commas
        if ',' in name:
            name = name.split(',')[0]
            
        # Clean up whitespace
        name = ' '.join(name.split())
        
        return name.strip()
    
    def search_for_quotes(self, name: str, max_results: int = 10) -> List[Dict[str, Any]]:
        """Search for quotes and writing samples for a given person."""
        clean_name = self.clean_name(name)
        logger.info(f"Searching for quotes from: {clean_name}")
        
        # Create search queries for different types of content
        search_queries = [
            f'"{clean_name}" interview quotes',
            f'"{clean_name}" speech transcript',
            f'"{clean_name}" article writing',
            f'"{clean_name}" twitter tweets',
            f'"{clean_name}" blog post writing'
        ]
        
        all_results = []
        
        for query in search_queries:
            try:
                logger.info(f"Searching with query: {query}")
                
                # Search for content
                search_results = self.exa.search_and_contents(
                    query=query,
                    type="auto",  # Use auto search type
                    num_results=3,  # Get fewer results per query to stay within limits
                    text=True,  # Get text content
                    livecrawl="preferred"  # Try to get fresh content
                )
                
                # Process results
                for result in search_results.results:
                    if result.text and len(result.text.strip()) > 50:  # Only include substantial content
                        all_results.append({
                            'url': result.url,
                            'title': result.title,
                            'text': result.text,
                            'published_date': getattr(result, 'published_date', None),
                            'query_used': query
                        })
                
                # Rate limiting
                time.sleep(self.rate_limit_delay)
                
            except Exception as e:
                logger.error(f"Error searching with query '{query}': {str(e)}")
                continue
        
        return all_results
    
    def extract_quotes(self, results: List[Dict[str, Any]], name: str, max_quotes: int = 3) -> List[Dict[str, Any]]:
        """Extract and format quotes from search results."""
        quotes = []
        clean_name = self.clean_name(name)
        
        for result in results:
            if len(quotes) >= max_quotes:
                break
                
            text = result['text']
            url = result['url']
            title = result['title']
            published_date = result.get('published_date', 'Unknown')
            
            # Try to extract meaningful excerpts (up to 200 words)
            words = text.split()
            if len(words) > 200:
                # Take first 200 words and try to end at a sentence
                excerpt_words = words[:200]
                excerpt = ' '.join(excerpt_words)
                
                # Try to end at a sentence
                last_period = excerpt.rfind('.')
                last_exclamation = excerpt.rfind('!')
                last_question = excerpt.rfind('?')
                
                last_sentence_end = max(last_period, last_exclamation, last_question)
                if last_sentence_end > len(excerpt) * 0.7:  # If we can end reasonably close to the end
                    excerpt = excerpt[:last_sentence_end + 1]
                else:
                    excerpt += "..."
            else:
                excerpt = text
            
            # Determine context type based on URL and title
            context = self.determine_context(url, title, result.get('query_used', ''))
            
            quotes.append({
                'Name': name,
                'Excerpt': excerpt.strip(),
                'Source_URL': url,
                'Date': published_date,
                'Context': context
            })
        
        return quotes
    
    def determine_context(self, url: str, title: str, query: str) -> str:
        """Determine the context type based on URL, title, and query."""
        url_lower = url.lower()
        title_lower = title.lower() if title else ""
        query_lower = query.lower()
        
        # Check for specific platforms/contexts
        if 'twitter.com' in url_lower or 'x.com' in url_lower:
            return 'Social media post (Twitter/X)'
        elif 'youtube.com' in url_lower:
            return 'Video/YouTube'
        elif 'substack.com' in url_lower:
            return 'Newsletter/Substack'
        elif 'medium.com' in url_lower:
            return 'Blog post (Medium)'
        elif 'interview' in title_lower or 'interview' in query_lower:
            return 'Interview'
        elif 'speech' in title_lower or 'transcript' in title_lower:
            return 'Speech/Transcript'
        elif 'article' in query_lower or 'blog' in query_lower:
            return 'Article/Blog post'
        elif 'podcast' in title_lower:
            return 'Podcast'
        else:
            return 'Web content'
    
    def process_csv(self, input_file: str, output_file: str, max_names: int = 10):
        """Process names from CSV and create output with quotes."""
        logger.info(f"Processing CSV file: {input_file}")
        
        # Read input CSV
        try:
            df = pd.read_csv(input_file)
            logger.info(f"Loaded {len(df)} rows from CSV")
        except Exception as e:
            logger.error(f"Error reading CSV file: {str(e)}")
            return
        
        # Take only the first max_names entries
        df_subset = df.head(max_names)
        logger.info(f"Processing first {len(df_subset)} names")
        
        # Prepare output data
        output_data = []
        
        for index, row in df_subset.iterrows():
            name = row['Name/Identifier']
            logger.info(f"Processing {index + 1}/{len(df_subset)}: {name}")
            
            try:
                # Search for quotes
                search_results = self.search_for_quotes(name)
                
                # Extract quotes
                quotes = self.extract_quotes(search_results, name)
                
                if quotes:
                    output_data.extend(quotes)
                    logger.info(f"Found {len(quotes)} quotes for {name}")
                else:
                    logger.warning(f"No quotes found for {name}")
                    # Add empty entry to maintain record
                    output_data.append({
                        'Name': name,
                        'Excerpt': 'No content found',
                        'Source_URL': '',
                        'Date': '',
                        'Context': 'No results'
                    })
                
            except Exception as e:
                logger.error(f"Error processing {name}: {str(e)}")
                output_data.append({
                    'Name': name,
                    'Excerpt': f'Error: {str(e)}',
                    'Source_URL': '',
                    'Date': '',
                    'Context': 'Error'
                })
            
            # Add delay between names to be respectful to the API
            time.sleep(2)
        
        # Save results to CSV
        try:
            output_df = pd.DataFrame(output_data)
            output_df.to_csv(output_file, index=False)
            logger.info(f"Results saved to {output_file}")
            logger.info(f"Total quotes collected: {len(output_data)}")
        except Exception as e:
            logger.error(f"Error saving results: {str(e)}")

def main():
    """Main function to run the scraper."""
    # Check for API key
    api_key = os.getenv('EXA_API_KEY')
    if not api_key:
        logger.error("EXA_API_KEY environment variable not found!")
        logger.error("Please create a .env file with your Exa API key:")
        logger.error("EXA_API_KEY=your_api_key_here")
        return
    
    # Initialize scraper
    scraper = ExaQuoteScraper(api_key)
    
    # File paths
    input_file = 'initial_classes.csv'
    output_file = f'exa_quotes_output_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
    
    # Process CSV (start with just 10 names)
    scraper.process_csv(input_file, output_file, max_names=10)
    
    logger.info("Scraping completed!")

if __name__ == "__main__":
    main()
