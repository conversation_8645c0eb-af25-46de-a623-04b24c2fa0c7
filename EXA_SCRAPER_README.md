# Exa API Quote Scraper

This script uses the Exa API to find quotes and writing samples for individuals listed in your CSV file.

## Setup

1. **Install dependencies:**
   ```bash
   pip install -r requirements_exa.txt
   ```

2. **Get an Exa API key:**
   - Go to https://dashboard.exa.ai/
   - Sign up and get your API key

3. **Create environment file:**
   ```bash
   cp .env.example .env
   ```
   Then edit `.env` and add your actual API key:
   ```
   EXA_API_KEY=your_actual_api_key_here
   ```

## Usage

Run the script to process the first 10 names from your CSV:

```bash
python exa_quotes_scraper.py
```

## What it does

For each person in your CSV, the script:

1. **Searches for multiple types of content:**
   - Interview quotes
   - Speech transcripts  
   - Articles and writing
   - Twitter/social media posts
   - Blog posts

2. **Extracts up to 3 examples per person:**
   - Raw text excerpts (up to 200 words)
   - Source URLs
   - Publication dates (when available)
   - Context type (interview, social media, article, etc.)

3. **Outputs structured CSV with columns:**
   - `Name`: Original name from input CSV
   - `Excerpt`: Text sample (up to 200 words)
   - `Source_URL`: Link to original source
   - `Date`: Publication date (when available)
   - `Context`: Type of content (interview, tweet, article, etc.)

## Output

The script creates a timestamped output file like:
`exa_quotes_output_20241227_143022.csv`

## Rate Limiting

The script includes built-in delays to respect API rate limits:
- 1 second between individual searches
- 2 seconds between processing different people

## Logging

All activity is logged to both console and `exa_scraper.log` file.

## Customization

You can modify these parameters in the script:
- `max_names`: Number of people to process (default: 10)
- `max_results`: Results per search query (default: 3)
- `max_quotes`: Quotes per person (default: 3)
- `rate_limit_delay`: Delay between API calls (default: 1 second)

## Troubleshooting

- **API Key Error**: Make sure your `.env` file has the correct API key
- **Rate Limiting**: If you get rate limit errors, increase the delays in the script
- **No Results**: Some people may not have much online content - this is normal
- **Connection Errors**: Check your internet connection and API key validity
