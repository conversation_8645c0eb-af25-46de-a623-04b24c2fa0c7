#!/usr/bin/env python3
"""
Lacan Structure Classifier using SVR (Support Vector Regression) with ordinal encoding
Maps structures to numbers: Detached=0, Psychotic=1, Neurotic=2, Pervert=3
Then rounds predictions back to discrete classes
"""

import pandas as pd
import numpy as np
from sklearn.svm import SVR
from sklearn.model_selection import StratifiedKFold, cross_val_score, GridSearchCV
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import classification_report, confusion_matrix, mean_squared_error
from sentence_transformers import SentenceTransformer
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

def load_and_filter_data(csv_path):
    """Load CSV and apply all filtering criteria"""
    print("Loading data from CSV...")
    df = pd.read_csv(csv_path)
    
    print(f"Original dataset size: {len(df)} entries")
    
    # Filter out Seminal structure (not enough data)
    df = df[df['Structure'] != 'Seminal']
    print(f"After removing Seminal structure: {len(df)} entries")
    
    # Filter out Taylor Swift (changed types)
    df = df[df['Name/Identifier'] != '<PERSON> Swift']
    print(f"After removing Taylor Swift: {len(df)} entries")
    
    # Filter out entries without proper quotes
    quote_filters = [
        r'\[Speaking example not available',
        r'\[Source link needed\]',
        r'\[Source name needed\]',
        'While specific extensive quotes',
        'Based on the available information'
    ]
    
    for filter_text in quote_filters:
        df = df[~df['Quote'].str.contains(filter_text, na=False, regex=True)]
    
    # Also filter out any entries with NaN quotes
    df = df.dropna(subset=['Quote'])
    
    print(f"After removing entries without proper quotes: {len(df)} entries")
    
    # Keep only the 4 main structures
    valid_structures = ['Detached', 'Psychotic', 'Neurotic', 'Pervert']
    df = df[df['Structure'].isin(valid_structures)]
    print(f"After keeping only main 4 structures: {len(df)} entries")
    
    # Show class distribution
    print("\nClass distribution:")
    print(df['Structure'].value_counts())
    
    return df

def preprocess_text(text):
    """Basic text preprocessing"""
    import re
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text)
    # Remove special characters but keep punctuation that might be meaningful
    text = re.sub(r'[^\w\s\.\!\?\,\;\:\-\'\"]', ' ', text)
    return text.strip()

def create_embeddings(texts, model_name='all-MiniLM-L6-v2'):
    """Create embeddings using MiniLM model"""
    print(f"Creating embeddings using {model_name}...")
    
    # Preprocess texts
    print("Preprocessing texts...")
    processed_texts = [preprocess_text(text) for text in texts]
    
    model = SentenceTransformer(model_name)
    embeddings = model.encode(processed_texts, show_progress_bar=True)
    print(f"Created embeddings with shape: {embeddings.shape}")
    return embeddings

def encode_structures_ordinal(structures):
    """Convert structure names to ordinal numbers"""
    # Define the mapping: Detached=0, Psychotic=1, Neurotic=2, Pervert=3
    structure_to_num = {
        'Detached': 0,
        'Psychotic': 1, 
        'Neurotic': 2,
        'Pervert': 3
    }
    
    num_to_structure = {v: k for k, v in structure_to_num.items()}
    
    encoded = [structure_to_num[s] for s in structures]
    
    print(f"\nOrdinal encoding mapping:")
    for struct, num in structure_to_num.items():
        count = structures.count(struct)
        print(f"{num}: {struct} ({count} samples)")
    
    return np.array(encoded), structure_to_num, num_to_structure

def train_and_evaluate_svr(X, y, cv_folds=5):
    """Train SVR with cross-validation and grid search"""
    print(f"\nTraining SVR with {cv_folds}-fold cross-validation...")
    
    # Scale features
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # Initialize SVR
    svr = SVR()
    
    # Grid search parameters
    param_grid = {
        'C': [0.1, 1.0, 10.0, 100.0],
        'kernel': ['rbf', 'linear', 'poly'],
        'gamma': ['scale', 'auto'],
        'epsilon': [0.01, 0.1, 0.2]
    }
    
    # For regression, we need to use StratifiedKFold based on rounded y values
    # to ensure balanced folds
    y_discrete = np.round(y).astype(int)
    skf = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42)
    
    print("Performing grid search for best SVR parameters...")
    grid_search = GridSearchCV(svr, param_grid, cv=skf, scoring='neg_mean_squared_error', n_jobs=-1)
    grid_search.fit(X_scaled, y)
    
    print(f"Best parameters: {grid_search.best_params_}")
    print(f"Best CV MSE: {-grid_search.best_score_:.4f}")
    
    # Get the best model
    best_svr = grid_search.best_estimator_
    
    # Perform cross-validation with best model for MSE
    from sklearn.model_selection import cross_val_predict
    y_pred_continuous = cross_val_predict(best_svr, X_scaled, y, cv=skf)
    
    # Round predictions to nearest integer for classification
    y_pred_discrete = np.round(y_pred_continuous).astype(int)
    # Clip to valid range [0, 3]
    y_pred_discrete = np.clip(y_pred_discrete, 0, 3)
    
    # Calculate MSE for regression performance
    mse = mean_squared_error(y, y_pred_continuous)
    print(f"Cross-validation MSE: {mse:.4f}")
    print(f"Cross-validation RMSE: {np.sqrt(mse):.4f}")
    
    # Calculate classification accuracy after rounding
    y_discrete = np.round(y).astype(int)
    accuracy = np.mean(y_discrete == y_pred_discrete)
    print(f"Classification accuracy after rounding: {accuracy:.4f}")
    
    return best_svr, y_pred_continuous, y_pred_discrete, mse

def plot_results(y_true_discrete, y_pred_discrete, y_true_continuous, y_pred_continuous, class_names):
    """Plot confusion matrix and regression scatter plot"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # Confusion Matrix
    cm = confusion_matrix(y_true_discrete, y_pred_discrete)
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=class_names, yticklabels=class_names, ax=ax1)
    ax1.set_title('Confusion Matrix (After Rounding)')
    ax1.set_xlabel('Predicted')
    ax1.set_ylabel('Actual')
    
    # Regression scatter plot
    ax2.scatter(y_true_continuous, y_pred_continuous, alpha=0.6)
    ax2.plot([0, 3], [0, 3], 'r--', label='Perfect prediction')
    ax2.set_xlabel('True Values')
    ax2.set_ylabel('Predicted Values')
    ax2.set_title('SVR Predictions vs True Values')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # Distribution of predictions
    ax3.hist(y_pred_continuous, bins=20, alpha=0.7, edgecolor='black')
    ax3.axvline(0, color='red', linestyle='--', alpha=0.7, label='Detached')
    ax3.axvline(1, color='orange', linestyle='--', alpha=0.7, label='Psychotic')
    ax3.axvline(2, color='green', linestyle='--', alpha=0.7, label='Neurotic')
    ax3.axvline(3, color='purple', linestyle='--', alpha=0.7, label='Pervert')
    ax3.set_xlabel('Predicted Values (Continuous)')
    ax3.set_ylabel('Frequency')
    ax3.set_title('Distribution of SVR Predictions')
    ax3.legend()
    
    # Residuals plot
    residuals = y_pred_continuous - y_true_continuous
    ax4.scatter(y_pred_continuous, residuals, alpha=0.6)
    ax4.axhline(0, color='red', linestyle='--')
    ax4.set_xlabel('Predicted Values')
    ax4.set_ylabel('Residuals')
    ax4.set_title('Residuals Plot')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('lacan_classifier_svr_results.png', dpi=300, bbox_inches='tight')
    print("Results plot saved as 'lacan_classifier_svr_results.png'")
    plt.close()

def main():
    """Main execution function"""
    # Load and filter data
    df = load_and_filter_data('enhanced_classes.csv')
    
    if len(df) == 0:
        print("No data remaining after filtering!")
        return
    
    # Prepare data
    texts = df['Quote'].tolist()
    structures = df['Structure'].tolist()
    
    # Create embeddings
    X = create_embeddings(texts)
    
    # Encode structures as ordinal numbers
    y, structure_to_num, num_to_structure = encode_structures_ordinal(structures)
    class_names = [num_to_structure[i] for i in range(4)]
    
    # Train and evaluate SVR
    model, y_pred_continuous, y_pred_discrete, mse = train_and_evaluate_svr(X, y)
    
    # Convert back to discrete for classification metrics
    y_discrete = np.round(y).astype(int)
    
    # Print detailed classification report
    print("\nDetailed Classification Report (after rounding):")
    print(classification_report(y_discrete, y_pred_discrete, target_names=class_names))
    
    # Plot results
    plot_results(y_discrete, y_pred_discrete, y, y_pred_continuous, class_names)
    
    # Show some example predictions
    print("\nSample predictions:")
    for i in range(min(10, len(df))):
        actual_num = y[i]
        predicted_continuous = y_pred_continuous[i]
        predicted_discrete = y_pred_discrete[i]
        
        actual_struct = num_to_structure[int(np.round(actual_num))]
        predicted_struct = num_to_structure[predicted_discrete]
        
        name = df.iloc[i]['Name/Identifier']
        quote_preview = texts[i][:100] + "..." if len(texts[i]) > 100 else texts[i]
        
        print(f"\nName: {name}")
        print(f"Quote: {quote_preview}")
        print(f"Actual: {actual_struct} ({actual_num})")
        print(f"Predicted: {predicted_struct} ({predicted_continuous:.3f} → {predicted_discrete})")
        print("-" * 50)

if __name__ == "__main__":
    main()
