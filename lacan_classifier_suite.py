#!/usr/bin/env python3
"""
Comprehensive Lacan Structure Classifier Suite
Organized system to run different classification approaches with cached embeddings
"""

import pandas as pd
import numpy as np
import pickle
import os
from pathlib import Path
from sklearn.svm import SVC, SVR
from sklearn.model_selection import StratifiedKFold, cross_val_score, GridSearchCV, cross_val_predict
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import classification_report, confusion_matrix, mean_squared_error, balanced_accuracy_score, f1_score
from sentence_transformers import SentenceTransformer
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

class LacanClassifierSuite:
    def __init__(self, csv_path='enhanced_classes.csv', cache_dir='cache'):
        self.csv_path = csv_path
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.df_filtered = None
        self.embeddings_cache = {}
        
    def load_and_filter_data(self):
        """Load CSV and apply all filtering criteria"""
        if self.df_filtered is not None:
            return self.df_filtered
            
        print("Loading data from CSV...")
        df = pd.read_csv(self.csv_path)
        
        print(f"Original dataset size: {len(df)} entries")
        
        # Apply filters
        df = df[df['Structure'] != 'Seminal']
        df = df[df['Name/Identifier'] != 'Taylor Swift']
        
        # Filter out entries without proper quotes
        quote_filters = [
            r'\[Speaking example not available',
            r'\[Source link needed\]',
            r'\[Source name needed\]',
            'While specific extensive quotes',
            'Based on the available information'
        ]
        
        for filter_text in quote_filters:
            df = df[~df['Quote'].str.contains(filter_text, na=False, regex=True)]
        
        df = df.dropna(subset=['Quote'])
        
        print(f"After filtering: {len(df)} entries")
        print("Structure distribution:")
        print(df['Structure'].value_counts())
        
        self.df_filtered = df
        return df
    
    def preprocess_text(self, text):
        """Basic text preprocessing"""
        import re
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'[^\w\s\.\!\?\,\;\:\-\'\"]', ' ', text)
        return text.strip()
    
    def get_embeddings(self, texts, cache_key, model_name='all-MiniLM-L6-v2'):
        """Get embeddings with caching"""
        cache_file = self.cache_dir / f"{cache_key}_embeddings.pkl"
        
        if cache_file.exists():
            print(f"Loading cached embeddings from {cache_file}")
            with open(cache_file, 'rb') as f:
                return pickle.load(f)
        
        print(f"Creating embeddings using {model_name}...")
        processed_texts = [self.preprocess_text(text) for text in texts]
        
        model = SentenceTransformer(model_name)
        embeddings = model.encode(processed_texts, show_progress_bar=True)
        
        # Cache the embeddings
        with open(cache_file, 'wb') as f:
            pickle.dump(embeddings, f)
        print(f"Cached embeddings to {cache_file}")
        
        return embeddings
    
    def prepare_data_original_4class(self):
        """Original 4-class approach"""
        df = self.load_and_filter_data()
        valid_structures = ['Detached', 'Psychotic', 'Neurotic', 'Pervert']
        df_subset = df[df['Structure'].isin(valid_structures)].copy()
        
        texts = df_subset['Quote'].tolist()
        labels = df_subset['Structure'].tolist()
        embeddings = self.get_embeddings(texts, 'original_4class')
        
        return embeddings, labels, df_subset, "Original 4-Class"
    
    def prepare_data_3class_balanced(self, strategy='median'):
        """3-class balanced approach"""
        df = self.load_and_filter_data()
        valid_structures = ['Detached', 'Psychotic', 'Neurotic']
        df_subset = df[df['Structure'].isin(valid_structures)].copy()
        
        # Balance classes
        class_counts = df_subset['Structure'].value_counts()
        if strategy == 'min':
            target_size = class_counts.min()
        elif strategy == 'median':
            target_size = int(class_counts.median())
        else:
            target_size = int(class_counts.mean())
        
        balanced_dfs = []
        for structure in class_counts.index:
            structure_df = df_subset[df_subset['Structure'] == structure]
            if len(structure_df) > target_size:
                sampled_df = structure_df.sample(n=target_size, random_state=42)
            else:
                sampled_df = structure_df
            balanced_dfs.append(sampled_df)
        
        df_balanced = pd.concat(balanced_dfs, ignore_index=True)
        
        texts = df_balanced['Quote'].tolist()
        labels = df_balanced['Structure'].tolist()
        embeddings = self.get_embeddings(texts, f'3class_balanced_{strategy}')
        
        return embeddings, labels, df_balanced, f"3-Class Balanced ({strategy})"
    
    def prepare_data_lumped_2class(self, balanced=False):
        """Lumped 2-class approach: (Detached+Psychotic) vs (Neurotic+Pervert)"""
        df = self.load_and_filter_data()

        # Create lumped categories
        df_lumped = df.copy()
        df_lumped['Lumped_Structure'] = df_lumped['Structure'].map({
            'Detached': 'Detached_Psychotic',
            'Psychotic': 'Detached_Psychotic',
            'Neurotic': 'Neurotic_Pervert',
            'Pervert': 'Neurotic_Pervert'
        })

        # Remove rows that don't map to our lumped categories
        df_lumped = df_lumped.dropna(subset=['Lumped_Structure'])

        if balanced:
            # Balance the lumped classes
            class_counts = df_lumped['Lumped_Structure'].value_counts()
            target_size = class_counts.min()

            balanced_dfs = []
            for structure in class_counts.index:
                structure_df = df_lumped[df_lumped['Lumped_Structure'] == structure]
                if len(structure_df) > target_size:
                    sampled_df = structure_df.sample(n=target_size, random_state=42)
                else:
                    sampled_df = structure_df
                balanced_dfs.append(sampled_df)

            df_lumped = pd.concat(balanced_dfs, ignore_index=True)
            cache_key = 'lumped_2class_balanced'
            display_name = "Lumped 2-Class Balanced"
        else:
            cache_key = 'lumped_2class'
            display_name = "Lumped 2-Class"

        texts = df_lumped['Quote'].tolist()
        labels = df_lumped['Lumped_Structure'].tolist()
        embeddings = self.get_embeddings(texts, cache_key)

        return embeddings, labels, df_lumped, display_name
    
    def prepare_data_svr_ordinal(self, encoding='original'):
        """SVR with ordinal encoding"""
        df = self.load_and_filter_data()
        valid_structures = ['Detached', 'Psychotic', 'Neurotic', 'Pervert']
        df_subset = df[df['Structure'].isin(valid_structures)].copy()
        
        # Define encodings
        encodings = {
            'original': {'Detached': 0, 'Psychotic': 1, 'Neurotic': 2, 'Pervert': 3},
            'severity': {'Detached': 0, 'Neurotic': 1, 'Pervert': 2, 'Psychotic': 3},
            'social': {'Psychotic': 0, 'Detached': 1, 'Pervert': 2, 'Neurotic': 3}
        }
        
        structure_to_num = encodings[encoding]
        labels = [structure_to_num[s] for s in df_subset['Structure']]
        
        texts = df_subset['Quote'].tolist()
        embeddings = self.get_embeddings(texts, f'svr_{encoding}')
        
        return embeddings, labels, df_subset, f"SVR {encoding.title()}"
    
    def train_svc_classifier(self, X, y, approach_name):
        """Train SVC classifier"""
        print(f"\nTraining SVC for {approach_name}...")
        
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # Determine if we need class balancing
        class_counts = Counter(y)
        is_balanced = len(set(class_counts.values())) == 1
        
        param_grid = {
            'C': [0.1, 1.0, 10.0],
            'kernel': ['rbf', 'linear'],
            'gamma': ['scale', 'auto'],
            'class_weight': [None] if is_balanced else [None, 'balanced']
        }
        
        svc = SVC(random_state=42)
        skf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        
        grid_search = GridSearchCV(svc, param_grid, cv=skf, scoring='accuracy', n_jobs=-1)
        grid_search.fit(X_scaled, y)
        
        best_svc = grid_search.best_estimator_
        cv_scores = cross_val_score(best_svc, X_scaled, y, cv=skf, scoring='accuracy')
        y_pred = cross_val_predict(best_svc, X_scaled, y, cv=skf)
        
        return {
            'model': best_svc,
            'cv_scores': cv_scores,
            'y_pred': y_pred,
            'best_params': grid_search.best_params_,
            'accuracy': cv_scores.mean(),
            'std': cv_scores.std()
        }
    
    def train_svr_regressor(self, X, y, approach_name):
        """Train SVR regressor"""
        print(f"\nTraining SVR for {approach_name}...")
        
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        param_grid = {
            'C': [0.1, 1.0, 10.0],
            'kernel': ['rbf', 'linear'],
            'gamma': ['scale', 'auto'],
            'epsilon': [0.01, 0.1]
        }
        
        svr = SVR()
        y_discrete = np.round(y).astype(int)
        skf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        
        grid_search = GridSearchCV(svr, param_grid, cv=skf, scoring='neg_mean_squared_error', n_jobs=-1)
        grid_search.fit(X_scaled, y)
        
        best_svr = grid_search.best_estimator_
        y_pred_continuous = cross_val_predict(best_svr, X_scaled, y, cv=skf)
        y_pred_discrete = np.round(y_pred_continuous).astype(int)
        y_pred_discrete = np.clip(y_pred_discrete, int(np.min(y)), int(np.max(y)))
        
        mse = mean_squared_error(y, y_pred_continuous)
        accuracy = np.mean(y_discrete == y_pred_discrete)
        
        return {
            'model': best_svr,
            'y_pred_continuous': y_pred_continuous,
            'y_pred': y_pred_discrete,
            'best_params': grid_search.best_params_,
            'mse': mse,
            'rmse': np.sqrt(mse),
            'accuracy': accuracy
        }
    
    def evaluate_approach(self, approach_name, method='svc'):
        """Evaluate a specific approach"""
        print(f"\n{'='*60}")
        print(f"EVALUATING: {approach_name}")
        print(f"{'='*60}")
        
        # Prepare data based on approach
        if approach_name == "original_4class":
            X, y, df_subset, display_name = self.prepare_data_original_4class()
        elif approach_name == "3class_balanced_min":
            X, y, df_subset, display_name = self.prepare_data_3class_balanced('min')
        elif approach_name == "3class_balanced_median":
            X, y, df_subset, display_name = self.prepare_data_3class_balanced('median')
        elif approach_name == "lumped_2class":
            X, y, df_subset, display_name = self.prepare_data_lumped_2class(balanced=False)
        elif approach_name == "lumped_2class_balanced":
            X, y, df_subset, display_name = self.prepare_data_lumped_2class(balanced=True)
        elif approach_name.startswith("svr_"):
            encoding = approach_name.split('_')[1]
            X, y, df_subset, display_name = self.prepare_data_svr_ordinal(encoding)
            method = 'svr'
        else:
            raise ValueError(f"Unknown approach: {approach_name}")
        
        print(f"Dataset size: {len(df_subset)}")
        print(f"Feature dimensions: {X.shape}")
        
        if method == 'svc':
            # Encode labels for SVC
            le = LabelEncoder()
            y_encoded = le.fit_transform(y)
            class_names = le.classes_
            
            print("Class distribution:")
            for i, class_name in enumerate(class_names):
                count = np.sum(y_encoded == i)
                print(f"  {class_name}: {count} samples")
            
            results = self.train_svc_classifier(X, y_encoded, display_name)
            results['class_names'] = class_names
            results['y_true'] = y_encoded

            # Calculate additional metrics
            balanced_acc = balanced_accuracy_score(results['y_true'], results['y_pred'])
            macro_f1 = f1_score(results['y_true'], results['y_pred'], average='macro')
            weighted_f1 = f1_score(results['y_true'], results['y_pred'], average='weighted')

            results['balanced_accuracy'] = balanced_acc
            results['macro_f1'] = macro_f1
            results['weighted_f1'] = weighted_f1

            print(f"\nResults:")
            print(f"Best parameters: {results['best_params']}")
            print(f"CV Accuracy: {results['accuracy']:.4f} (±{results['std']*2:.4f})")
            print(f"Balanced Accuracy: {balanced_acc:.4f}")
            print(f"Macro F1: {macro_f1:.4f}")
            print(f"Weighted F1: {weighted_f1:.4f}")
            print(f"\nClassification Report:")
            print(classification_report(results['y_true'], results['y_pred'],
                                      target_names=class_names, zero_division=0))
            
        else:  # SVR
            results = self.train_svr_regressor(X, y, display_name)
            results['y_true'] = np.round(y).astype(int)
            
            print(f"\nResults:")
            print(f"Best parameters: {results['best_params']}")
            print(f"MSE: {results['mse']:.4f}, RMSE: {results['rmse']:.4f}")
            print(f"Classification Accuracy: {results['accuracy']:.4f}")
        
        results['approach_name'] = approach_name
        results['display_name'] = display_name
        results['method'] = method
        results['dataset_size'] = len(df_subset)
        
        return results
    
    def run_comparison(self, approaches=None):
        """Run comparison of multiple approaches"""
        if approaches is None:
            approaches = [
                "original_4class",
                "3class_balanced_median",
                "lumped_2class",
                "lumped_2class_balanced",
                "svr_original"
            ]
        
        results = {}
        
        for approach in approaches:
            try:
                results[approach] = self.evaluate_approach(approach)
            except Exception as e:
                print(f"Error with {approach}: {e}")
                continue
        
        # Summary comparison
        print(f"\n{'='*80}")
        print("SUMMARY COMPARISON")
        print(f"{'='*80}")
        
        print(f"{'Approach':<25} {'Method':<6} {'Size':<6} {'Accuracy':<10} {'Bal.Acc':<8} {'Notes'}")
        print("-" * 90)

        for approach, result in results.items():
            method = result['method'].upper()
            size = result['dataset_size']
            if method == 'SVC':
                accuracy = f"{result['accuracy']:.4f}"
                bal_acc = f"{result.get('balanced_accuracy', 0):.4f}" if 'balanced_accuracy' in result else "N/A"
                notes = f"±{result['std']*2:.4f}"
            else:
                accuracy = f"{result['accuracy']:.4f}"
                bal_acc = "N/A"
                notes = f"RMSE:{result['rmse']:.3f}"

            print(f"{result['display_name']:<25} {method:<6} {size:<6} {accuracy:<10} {bal_acc:<8} {notes}")
        
        # Find best approach
        svc_results = {k: v for k, v in results.items() if v['method'] == 'svc'}
        if svc_results:
            best_approach = max(svc_results.keys(), key=lambda x: svc_results[x]['accuracy'])
            best_accuracy = svc_results[best_approach]['accuracy']
            print(f"\nBest SVC approach: {svc_results[best_approach]['display_name']} ({best_accuracy:.4f})")
        
        return results

def main():
    """Main function with command-line interface"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Lacan Structure Classifier Suite')
    parser.add_argument('--approach', type=str, 
                       choices=['original_4class', '3class_balanced_min', '3class_balanced_median', 
                               'lumped_2class', 'svr_original', 'svr_severity', 'svr_social', 'all'],
                       default='all', help='Which approach to run')
    parser.add_argument('--cache-dir', type=str, default='cache', help='Directory for caching embeddings')
    
    args = parser.parse_args()
    
    suite = LacanClassifierSuite(cache_dir=args.cache_dir)
    
    if args.approach == 'all':
        suite.run_comparison()
    else:
        suite.evaluate_approach(args.approach)

if __name__ == "__main__":
    main()
